/* Restaurant Header */
.restaurant-header {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  text-align: left;
  font-size: 1rem;
  text-indent: 0.5rem;
  margin-left: 0rem;
  margin-bottom: 0.2rem;
  padding: 0rem;
  background-color: var(--background-light);
}

/* Category Navigation */
.category-nav {
  position: fixed;
  top: 3rem; /* Adjust based on the height of the header */
  left: 0;
  right: 0;
  z-index: 999;
  background-color: var(--background-lighter);
  box-sizing: border-box;
  padding: 0.5rem 0;
  overflow-x: auto;
  overflow-y: hidden;
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.category-nav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.category-nav ul {
  list-style: none;
  margin: 0;
  padding: 0 0.5rem;
  display: flex;
  white-space: nowrap;
  scroll-behavior: smooth;
  /* Force the ul to be wider than its container */
  width: fit-content;
  min-width: 100%;
}

.category-item {
  display: inline-block;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
  border-radius: 1rem;
  margin-right: 0.5rem;
  font-weight: 500;
}

.category-item:hover {
  background-color: var(--primary-color-light, rgba(var(--primary-color), 0.1));
}

.category-item.active {
  background-color: var(--primary-color);
  color: var(--background-white);
  font-weight: 600;
}

/* Menu Section */
.menu-section {
  margin-left: 0.5rem;
  margin-bottom: 0.5rem;
  text-align: left;
}

/* Menu Items within a Category */
.menu-items {
  display: grid;
  gap: 0.5rem;
  grid-template-columns: repeat(auto-fill, minmax(20em, 1fr));
  padding: 0.5rem;
}

/* Menu Item */
.menu-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border: 0.1rem solid var(--border-color);
  border-radius: 0.5rem;
  background-color: var(--background-white);
  transition: box-shadow 0.3s;
}

.menu-item:hover {
  box-shadow: 0 0.25rem 0.5rem var(--shadow-color);
}

.menu-item img {
  max-width: 40%;
  height: auto;
  border-radius: 0.5rem;
  margin-right: 0.5rem;
}

.menu-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.menu-item-title {
  font-size: 1.25rem;
  margin: 0.5rem 0;
  text-align: left;
}

.menu-item-description {
  font-size: 1rem;
  color: var(--text-secondary);
  text-align: left;
  margin-bottom: 0.5rem;
}

.price-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.price {
  font-weight: bold;
  color: var(--text-primary);
  text-align: right;
  margin-right: 1rem;
  margin-top: 0rem;
  margin-bottom: 0rem;
}

/* Add to Cart Button */
.add-to-cart-button {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--background-white);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  transition: background-color 0.3s;
}

.add-to-cart-button:hover {
  background-color: var(--primary-color-dark);
}

/* Menu Container */
.menu-container {
  height: calc(100vh - 6rem); /* 减去header和nav的高度 */
  margin-top: 6rem; /* 为fixed header和nav留出空间 */
  overflow-y: auto;
  background-color: var(--background-light);
  scroll-behavior: smooth;
  position: relative;
}

/* Menu Grid */
.menu-grid {
  padding: 1rem 0 2rem 0; /* 添加顶部和底部padding */
}

.menu-category.active {
  background-color: var(--background-white);
}

.menu-category:hover {
  background-color: var(--background-lighter);
}

.menu-items-grid {
  border: 0.1rem solid var(--border-color);
  background-color: var(--background-white);
  box-shadow: 0 0.25rem 0.5rem var(--shadow-color);
}

.menu-item-description {
  color: var(--text-secondary);
}

.menu-item-price {
  color: var(--text-primary);
}

.add-to-cart-btn {
  background-color: var(--primary-color);
}

.add-to-cart-btn:hover {
  background-color: var(--primary-color-dark);
}
